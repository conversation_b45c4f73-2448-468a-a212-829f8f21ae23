import { type NextRequest, NextResponse } from "next/server"
import { deleteMindMap, getMindMapById } from "@/lib/database"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params
    const mindMapFromDb = await getMindMapById(id)

    if (!mindMapFromDb) {
      return NextResponse.json({ error: "Mind map not found" }, { status: 404 })
    }

    const mindMap = {
      id: mindMapFromDb.id,
      titulo: mindMapFromDb.name,
      dados: mindMapFromDb.data,
      criado_em: new Date(mindMapFromDb.created_at).toISOString(),
      atualizado_em: new Date(mindMapFromDb.updated_at).toISOString(),
      descricao: mindMapFromDb.description,
    }

    return NextResponse.json({ mindMap })
  } catch (error) {
    console.error("Error getting mind map:", error)
    return NextResponse.json({ error: "Failed to get mind map" }, { status: 500 })
  }
}
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params
    await deleteMindMap(id)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting mind map:", error)
    return NextResponse.json({ error: "Failed to delete mind map" }, { status: 500 })
  }
}
