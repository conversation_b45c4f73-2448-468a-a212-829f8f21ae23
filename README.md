# Mindscape

Uma aplicação de mapas mentais inteligente que usa IA para ajudar na criação e organização de ideias.

## 🚀 Características

- **Interface Intuitiva**: Canvas interativo para criar e editar mapas mentais
- **IA Integrada**: Suporte para OpenRouter e Ollama para geração de conteúdo
- **Banco Local**: SQLite para armazenamento simples e confiável
- **Configuração Fácil**: Setup inicial com configurações pré-definidas
- **Temas**: Suporte a tema claro e escuro
- **Proteção por Senha**: Mapas mentais podem ser protegidos

## 📦 Instalação

1. Clone o repositório:
\`\`\`bash
git clone <url-do-repositorio>
cd mindmap-ia
\`\`\`

2. Instale as dependências:
\`\`\`bash
npm install
\`\`\`

3. Execute em desenvolvimento:
\`\`\`bash
npm run dev
\`\`\`

4. Acesse http://localhost:3000

## ⚙️ Configuração

### Primeira Execução

1. Abra a aplicação
2. Clique no ícone de configurações (⚙️)
3. Configure seu provedor de IA:
   - **OpenRouter**: Insira sua chave da API
   - **Ollama**: Configure a URL do servidor local

### Variáveis de Ambiente (Opcionais)

Crie um arquivo `.env.local`:

\`\`\`env
# Diretório do banco de dados (opcional)
DATA_DIR=./data

# Ambiente
NODE_ENV=development
\`\`\`

## 🤖 Provedores de IA

### OpenRouter (Recomendado)
- Acesse [openrouter.ai](https://openrouter.ai)
- Crie uma conta e obtenha sua chave da API
- Configure na interface da aplicação

### Ollama (Local)
- Instale o Ollama em sua máquina
- Execute: `ollama serve`
- Configure a URL na aplicação (padrão: http://localhost:11434)

## 💾 Banco de Dados

A aplicação usa SQLite local:
- Arquivo: `./data/mindmap.db` (ou conforme DATA_DIR)
- Criado automaticamente na primeira execução
- Backup manual copiando o arquivo

## 🎨 Funcionalidades

- **Criação de Nós**: Clique no canvas ou use o chat
- **Conexões**: Arraste entre nós para conectar
- **Chat IA**: Converse para gerar ideias e expandir mapas
- **Zoom e Pan**: Navegue pelo canvas facilmente
- **Salvar/Carregar**: Persistência automática ou manual
- **Proteção**: Mapas podem ser protegidos por senha

## 🔧 Desenvolvimento

\`\`\`bash
# Desenvolvimento
npm run dev

# Build para produção
npm run build

# Executar produção
npm start

# Linting
npm run lint
\`\`\`

## 📁 Estrutura

\`\`\`
├── app/                 # Next.js App Router
├── components/          # Componentes React
├── lib/                 # Utilitários e serviços
├── types/               # Tipos TypeScript
├── data/                # Banco de dados SQLite
└── public/              # Arquivos estáticos
\`\`\`

## 🐛 Solução de Problemas

### Banco não conecta
- Verifique permissões da pasta `data/`
- Tente deletar `data/mindmap.db` para recriar

### IA não responde
- Verifique a chave da API (OpenRouter)
- Confirme se Ollama está rodando (Local)
- Teste a conexão nas configurações

### Performance lenta
- Limite o número de nós no mapa
- Use zoom para focar em áreas específicas

## 📄 Licença

MIT License - veja LICENSE para detalhes.
