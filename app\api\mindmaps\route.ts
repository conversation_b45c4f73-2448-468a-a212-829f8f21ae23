import { type NextRequest, NextResponse } from "next/server"
import { getMindMaps, saveMindMap } from "@/lib/database"

export async function GET() {
  try {
    const mindMapsFromDb = await getMindMaps()
    const mindMaps = mindMapsFromDb.map((m: any) => ({
      id: m.id,
      titulo: m.name,
      dados: m.data,
      criado_em: new Date(m.created_at).toISOString(),
      atualizado_em: new Date(m.updated_at).toISOString(),
      descricao: m.description,
    }))
    return NextResponse.json({ mindMaps })
  } catch (error) {
    console.error("Error getting mind maps:", error)
    return NextResponse.json({ error: "Failed to get mind maps" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, titulo, descricao, dados } = body

    if (!titulo || !dados) {
      return NextResponse.json({ error: "Título e dados são obrigatórios" }, { status: 400 })
    }

    const savedId = await saveMindMap({
      id,
      name: titulo,
      description: descricao,
      data: dados,
    })

    return NextResponse.json({ success: true, id: savedId })
  } catch (error) {
    console.error("Error saving mind map:", error)
    return NextResponse.json({ error: "Failed to save mind map" }, { status: 500 })
  }
}
