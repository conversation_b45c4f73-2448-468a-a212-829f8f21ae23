import { neon } from "@neondatabase/serverless"
import { join } from "path"
import { promises as fs } from "fs"

export interface Config {
  id?: number
  language: string
  database_type: "json" | "neon"
  database_url?: string
  llm_provider: "openrouter" | "ollama"
  openrouter_api_key?: string
  ollama_base_url?: string
  default_model?: string
  setup_completed?: boolean
  created_at?: Date
  updated_at?: Date
}

export interface MindMap {
  id?: string
  name: string
  description?: string
  data: any
  created_at: Date
  updated_at: Date
}

export let db: any = null
let dbType: "json" | "neon" = "json"

// JSON file paths
const configPath = join(process.cwd(), "data", "config.json")
const mindMapsPath = join(process.cwd(), "data", "mindmaps.json")

// Ensure data directory exists
async function ensureDataDir() {
  const dataDir = join(process.cwd(), "data")
  try {
    await fs.access(dataDir)
  } catch {
    await fs.mkdir(dataDir, { recursive: true })
  }
}

// JSON database helpers
async function readJsonFile(path: string, defaultValue: any = []) {
  try {
    const data = await fs.readFile(path, "utf-8")
    return JSON.parse(data)
  } catch {
    return defaultValue
  }
}

async function writeJsonFile(path: string, data: any) {
  await ensureDataDir()
  await fs.writeFile(path, JSON.stringify(data, null, 2))
}

export async function initializeDatabase() {
  if (db) return db

  // Check if we have a Neon database URL
  const databaseUrl = process.env.DATABASE_URL

  if (databaseUrl && databaseUrl.includes("neon.tech")) {
    // Use Neon database
    dbType = "neon"
    db = neon(databaseUrl)

    // Initialize tables for Neon
    await db(`
      CREATE TABLE IF NOT EXISTS config (
        id SERIAL PRIMARY KEY,
        language VARCHAR(10) DEFAULT 'pt',
        database_type VARCHAR(20) DEFAULT 'neon',
        database_url TEXT,
        llm_provider VARCHAR(20) DEFAULT 'openrouter',
        openrouter_api_key TEXT,
        ollama_base_url TEXT DEFAULT 'http://localhost:11434',
        default_model VARCHAR(100) DEFAULT 'openai/gpt-4o-mini',
        setup_completed BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    await db(`
      CREATE TABLE IF NOT EXISTS mind_maps (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        data JSONB NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Add setup_completed column if it doesn't exist for Neon (ignore errors)
    try {
      await db(`ALTER TABLE config ADD COLUMN setup_completed BOOLEAN DEFAULT FALSE`);
    } catch (e) { /* ignore */ }

  } else {
    // Use JSON file database
    dbType = "json"
    await ensureDataDir()

    // Initialize JSON files if they don't exist
    const configData = await readJsonFile(configPath, {})
    const mindMapsData = await readJsonFile(mindMapsPath, [])

    // Ensure default config exists
    if (!configData.id) {
      const defaultConfig = {
        id: 1,
        language: "pt",
        database_type: "json",
        database_url: "",
        llm_provider: "openrouter",
        openrouter_api_key: "",
        ollama_base_url: "http://localhost:11434",
        default_model: "openai/gpt-4o-mini",
        setup_completed: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      await writeJsonFile(configPath, defaultConfig)
    }

    db = { type: "json" } // Placeholder object
  }

  // Ensure default config exists with Portuguese
  await ensureDefaultConfig()

  return db
}
console.log("Initialized db object:", db);

async function ensureDefaultConfig() {
  try {
    if (dbType === "neon") {
      const result = await db("SELECT * FROM config LIMIT 1")
      const existingConfig = result[0]

      if (!existingConfig) {
        const defaultConfig = {
          language: "pt",
          database_type: "neon",
          database_url: process.env.DATABASE_URL || null,
          llm_provider: "openrouter",
          openrouter_api_key: null,
          ollama_base_url: "http://localhost:11434",
          default_model: "openai/gpt-4o-mini",
        }

        await db(
          `
          INSERT INTO config (language, database_type, database_url, llm_provider, openrouter_api_key, ollama_base_url, default_model)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
        `,
          [
            defaultConfig.language,
            defaultConfig.database_type,
            defaultConfig.database_url,
            defaultConfig.llm_provider,
            defaultConfig.openrouter_api_key,
            defaultConfig.ollama_base_url,
            defaultConfig.default_model,
          ],
        )
      } else if (existingConfig.language !== "pt") {
        // Force Portuguese if not set
        await db("UPDATE config SET language = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2", [
          "pt",
          existingConfig.id,
        ])
      }
    }
    // For JSON, default config is already handled in initializeDatabase
  } catch (error) {
    console.error("Error ensuring default config:", error)
  }
}

export async function getDatabase() {
  return await initializeDatabase()
}

export async function getConfig(): Promise<Config> {
  await initializeDatabase()

  try {
    let config

    if (dbType === "neon") {
      const result = await db("SELECT * FROM config LIMIT 1")
      config = result[0]

      if (!config) {
        await ensureDefaultConfig()
        const newResult = await db("SELECT * FROM config LIMIT 1")
        config = newResult[0]
      }
    } else {
      // JSON file database
      config = await readJsonFile(configPath, {
        id: 1,
        language: "pt",
        database_type: "json",
        llm_provider: "openrouter",
        ollama_base_url: "http://localhost:11434",
        default_model: "openai/gpt-4o-mini",
        setup_completed: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    }

    // Ensure Portuguese is set
    if (config.language !== "pt") {
      config.language = "pt"
      await saveConfig(config)
    }

    return config
  } catch (error) {
    console.error("Error getting config:", error)
    // Return default config with Portuguese
    return {
      language: "pt",
      database_type: dbType,
      llm_provider: "openrouter",
      ollama_base_url: "http://localhost:11434",
      default_model: "openai/gpt-4o-mini",
      setup_completed: false
    }
  }
}

export async function saveConfig(config: Partial<Config>): Promise<void> {
  await initializeDatabase()

  try {
    // Force Portuguese
    config.language = "pt"

    if (dbType === "neon") {
      const existing = await db("SELECT id FROM config LIMIT 1")

      if (existing.length > 0) {
        await db(
          `
          UPDATE config SET
            language = $1,
            database_type = $2,
            database_url = $3,
            llm_provider = $4,
            openrouter_api_key = $5,
            ollama_base_url = $6,
            default_model = $7,
            setup_completed = $8,
            updated_at = CURRENT_TIMESTAMP
          WHERE id = $9
        `,
          [
            config.language || "pt",
            config.database_type || "neon",
            config.database_url,
            config.llm_provider || "openrouter",
            config.openrouter_api_key,
            config.ollama_base_url || "http://localhost:11434",
            config.default_model || "openai/gpt-4o-mini",
            config.setup_completed === true, // Ensure boolean
            existing[0].id,
          ],
        )
      } else {
        await db(
          `
          INSERT INTO config (language, database_type, database_url, llm_provider, openrouter_api_key, ollama_base_url, default_model, setup_completed)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        `,
          [
            config.language || "pt",
            config.database_type || "neon",
            config.database_url,
            config.llm_provider || "openrouter",
            config.openrouter_api_key,
            config.ollama_base_url || "http://localhost:11434",
            config.default_model || "openai/gpt-4o-mini",
            config.setup_completed === true, // Ensure boolean
          ],
        )
      }
    } else {
      // JSON file database
      const existingConfig = await readJsonFile(configPath, {})

      const updatedConfig = {
        ...existingConfig,
        ...config,
        language: "pt", // Force Portuguese
        database_type: "json",
        updated_at: new Date().toISOString()
      }

      // Ensure we have an ID
      if (!updatedConfig.id) {
        updatedConfig.id = 1
        updatedConfig.created_at = new Date().toISOString()
      }

      await writeJsonFile(configPath, updatedConfig)
    }
  } catch (error) {
    console.error("Error saving config:", error)
    throw error
  }
}

export async function saveMindMap(mindMap: Omit<MindMap, "created_at" | "updated_at">): Promise<string> {
    await initializeDatabase();
    const now = new Date();

    if ('id' in mindMap && mindMap.id) {
        // Update existing mind map
        try {
            if (dbType === "neon") {
                await db('UPDATE mind_maps SET name = $1, description = $2, data = $3, updated_at = $4 WHERE id = $5', [mindMap.name, mindMap.description, JSON.stringify(mindMap.data), now, mindMap.id]);
            } else {
                // JSON file database
                const mindMaps = await readJsonFile(mindMapsPath, []);
                const index = mindMaps.findIndex((m: any) => m.id === mindMap.id);
                if (index !== -1) {
                    mindMaps[index] = {
                        ...mindMaps[index],
                        name: mindMap.name,
                        description: mindMap.description,
                        data: mindMap.data,
                        updated_at: now.toISOString()
                    };
                    await writeJsonFile(mindMapsPath, mindMaps);
                }
            }
            return mindMap.id as string;
        } catch (error) {
            console.error("Error updating mind map:", error);
            throw error;
        }
    } else {
        // Create new mind map
        const id = crypto.randomUUID();
        try {
            if (dbType === "neon") {
                await db('INSERT INTO mind_maps (id, name, description, data, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6)', [id, mindMap.name, mindMap.description, JSON.stringify(mindMap.data), now, now]);
            } else {
                // JSON file database
                const mindMaps = await readJsonFile(mindMapsPath, []);
                const newMindMap = {
                    id,
                    name: mindMap.name,
                    description: mindMap.description,
                    data: mindMap.data,
                    created_at: now.toISOString(),
                    updated_at: now.toISOString()
                };
                mindMaps.push(newMindMap);
                await writeJsonFile(mindMapsPath, mindMaps);
            }
            return id;
        } catch (error) {
            console.error("Error saving new mind map:", error);
            throw error;
        }
    }
}

export async function getMindMaps(): Promise<MindMap[]> {
  await initializeDatabase()

  try {
    let mindMaps

    if (dbType === "neon") {
      mindMaps = await db("SELECT * FROM mind_maps ORDER BY created_at DESC")
    } else {
      // JSON file database
      mindMaps = await readJsonFile(mindMapsPath, [])
      // Sort by created_at DESC
      mindMaps.sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    }

    return mindMaps.map((row: any) => ({
      ...row,
      data: typeof row.data === "string" ? JSON.parse(row.data) : row.data,
      created_at: new Date(row.created_at),
      updated_at: new Date(row.updated_at),
    }))
  } catch (error) {
    console.error("Error getting mind maps:", error)
    return []
  }
}

export async function getMindMapById(id: string): Promise<MindMap | null> {
  await initializeDatabase()

  try {
    let mindMap

    if (dbType === "neon") {
      const result = await db("SELECT * FROM mind_maps WHERE id = $1", [id])
      mindMap = result[0]
    } else {
      // JSON file database
      const mindMaps = await readJsonFile(mindMapsPath, [])
      mindMap = mindMaps.find((m: any) => m.id === id)
    }

    if (!mindMap) {
      return null
    }

    return {
      ...mindMap,
      data: typeof mindMap.data === "string" ? JSON.parse(mindMap.data) : mindMap.data,
      created_at: new Date(mindMap.created_at),
      updated_at: new Date(mindMap.updated_at),
    }
  } catch (error) {
    console.error(`Error getting mind map with id ${id}:`, error)
    return null
  }
}

export async function deleteMindMap(id: string): Promise<void> {
  await initializeDatabase()

  try {
    if (dbType === "neon") {
      await db("DELETE FROM mind_maps WHERE id = $1", [id])
    } else {
      // JSON file database
      const mindMaps = await readJsonFile(mindMapsPath, [])
      const filteredMindMaps = mindMaps.filter((m: any) => m.id !== id)
      await writeJsonFile(mindMapsPath, filteredMindMaps)
    }
  } catch (error) {
    console.error("Error deleting mind map:", error)
    throw error
  }
}

export async function clearAllMindMaps(): Promise<void> {
  await initializeDatabase()

  try {
    if (dbType === "neon") {
      await db("DELETE FROM mind_maps")
    } else {
      // JSON file database
      await writeJsonFile(mindMapsPath, [])
    }
  } catch (error) {
    console.error("Error clearing mind maps:", error)
    throw error
  }
}
