import { neon } from "@neondatabase/serverless"
import { join } from "path"
import { promises as fs } from "fs"
import bcrypt from "bcryptjs"
import { generateId } from "./utils"
import type { User, SharedMindMap } from "@/types/mindmap"

export interface Config {
  id?: number
  language: string
  database_type: "json" | "neon"
  database_url?: string
  llm_provider: "openrouter" | "ollama"
  openrouter_api_key?: string
  ollama_base_url?: string
  default_model?: string
  setup_completed?: boolean
  created_at?: Date
  updated_at?: Date
}

export interface MindMap {
  id?: string
  name: string
  description?: string
  data: any
  user_id?: string
  created_at: Date
  updated_at: Date
}

export let db: any = null
let dbType: "json" | "neon" = "json"

// JSON file paths
const configPath = join(process.cwd(), "data", "config.json")
const mindMapsPath = join(process.cwd(), "data", "mindmaps.json")

// Ensure data directory exists
async function ensureDataDir() {
  const dataDir = join(process.cwd(), "data")
  try {
    await fs.access(dataDir)
  } catch {
    await fs.mkdir(dataDir, { recursive: true })
  }
}

// JSON database helpers
async function readJsonFile(path: string, defaultValue: any = []) {
  try {
    const data = await fs.readFile(path, "utf-8")
    return JSON.parse(data)
  } catch {
    return defaultValue
  }
}

async function writeJsonFile(path: string, data: any) {
  await ensureDataDir()
  await fs.writeFile(path, JSON.stringify(data, null, 2))
}

export async function initializeDatabase() {
  if (db) return db

  // Check if we have a Neon database URL
  const databaseUrl = process.env.DATABASE_URL

  if (databaseUrl && databaseUrl.includes("neon.tech")) {
    // Use Neon database
    dbType = "neon"
    db = neon(databaseUrl)

    // Initialize tables for Neon
    await db(`
      CREATE TABLE IF NOT EXISTS config (
        id SERIAL PRIMARY KEY,
        language VARCHAR(10) DEFAULT 'pt',
        database_type VARCHAR(20) DEFAULT 'neon',
        database_url TEXT,
        llm_provider VARCHAR(20) DEFAULT 'openrouter',
        openrouter_api_key TEXT,
        ollama_base_url TEXT DEFAULT 'http://localhost:11434',
        default_model VARCHAR(100) DEFAULT 'openai/gpt-4o-mini',
        setup_completed BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    await db(`
      CREATE TABLE IF NOT EXISTS mind_maps (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        data JSONB NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Add setup_completed column if it doesn't exist for Neon (ignore errors)
    try {
      await db(`ALTER TABLE config ADD COLUMN setup_completed BOOLEAN DEFAULT FALSE`);
    } catch (e) { /* ignore */ }

  } else {
    // Use JSON file database
    dbType = "json"
    await ensureDataDir()

    // Initialize JSON files if they don't exist
    const configData = await readJsonFile(configPath, {})
    const mindMapsData = await readJsonFile(mindMapsPath, [])

    // Ensure default config exists
    if (!configData.id) {
      const defaultConfig = {
        id: 1,
        language: "pt",
        database_type: "json",
        database_url: "",
        llm_provider: "openrouter",
        openrouter_api_key: "",
        ollama_base_url: "http://localhost:11434",
        default_model: "openai/gpt-4o-mini",
        setup_completed: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      await writeJsonFile(configPath, defaultConfig)
    }

    db = { type: "json" } // Placeholder object
  }

  // Ensure default config exists with Portuguese
  await ensureDefaultConfig()

  return db
}
console.log("Initialized db object:", db);

async function ensureDefaultConfig() {
  try {
    if (dbType === "neon") {
      const result = await db("SELECT * FROM config LIMIT 1")
      const existingConfig = result[0]

      if (!existingConfig) {
        const defaultConfig = {
          language: "pt",
          database_type: "neon",
          database_url: process.env.DATABASE_URL || null,
          llm_provider: "openrouter",
          openrouter_api_key: null,
          ollama_base_url: "http://localhost:11434",
          default_model: "openai/gpt-4o-mini",
        }

        await db(
          `
          INSERT INTO config (language, database_type, database_url, llm_provider, openrouter_api_key, ollama_base_url, default_model)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
        `,
          [
            defaultConfig.language,
            defaultConfig.database_type,
            defaultConfig.database_url,
            defaultConfig.llm_provider,
            defaultConfig.openrouter_api_key,
            defaultConfig.ollama_base_url,
            defaultConfig.default_model,
          ],
        )
      } else if (existingConfig.language !== "pt") {
        // Force Portuguese if not set
        await db("UPDATE config SET language = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2", [
          "pt",
          existingConfig.id,
        ])
      }
    }
    // For JSON, default config is already handled in initializeDatabase
  } catch (error) {
    console.error("Error ensuring default config:", error)
  }
}

export async function getDatabase() {
  return await initializeDatabase()
}

export async function getConfig(): Promise<Config> {
  await initializeDatabase()

  try {
    let config

    if (dbType === "neon") {
      const result = await db("SELECT * FROM config LIMIT 1")
      config = result[0]

      if (!config) {
        await ensureDefaultConfig()
        const newResult = await db("SELECT * FROM config LIMIT 1")
        config = newResult[0]
      }
    } else {
      // JSON file database
      config = await readJsonFile(configPath, {
        id: 1,
        language: "pt",
        database_type: "json",
        llm_provider: "openrouter",
        ollama_base_url: "http://localhost:11434",
        default_model: "openai/gpt-4o-mini",
        setup_completed: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    }

    // Ensure Portuguese is set
    if (config.language !== "pt") {
      config.language = "pt"
      await saveConfig(config)
    }

    return config
  } catch (error) {
    console.error("Error getting config:", error)
    // Return default config with Portuguese
    return {
      language: "pt",
      database_type: dbType,
      llm_provider: "openrouter",
      ollama_base_url: "http://localhost:11434",
      default_model: "openai/gpt-4o-mini",
      setup_completed: false
    }
  }
}

export async function saveConfig(config: Partial<Config>): Promise<void> {
  await initializeDatabase()

  try {
    // Force Portuguese
    config.language = "pt"

    if (dbType === "neon") {
      const existing = await db("SELECT id FROM config LIMIT 1")

      if (existing.length > 0) {
        await db(
          `
          UPDATE config SET
            language = $1,
            database_type = $2,
            database_url = $3,
            llm_provider = $4,
            openrouter_api_key = $5,
            ollama_base_url = $6,
            default_model = $7,
            setup_completed = $8,
            updated_at = CURRENT_TIMESTAMP
          WHERE id = $9
        `,
          [
            config.language || "pt",
            config.database_type || "neon",
            config.database_url,
            config.llm_provider || "openrouter",
            config.openrouter_api_key,
            config.ollama_base_url || "http://localhost:11434",
            config.default_model || "openai/gpt-4o-mini",
            config.setup_completed === true, // Ensure boolean
            existing[0].id,
          ],
        )
      } else {
        await db(
          `
          INSERT INTO config (language, database_type, database_url, llm_provider, openrouter_api_key, ollama_base_url, default_model, setup_completed)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        `,
          [
            config.language || "pt",
            config.database_type || "neon",
            config.database_url,
            config.llm_provider || "openrouter",
            config.openrouter_api_key,
            config.ollama_base_url || "http://localhost:11434",
            config.default_model || "openai/gpt-4o-mini",
            config.setup_completed === true, // Ensure boolean
          ],
        )
      }
    } else {
      // JSON file database
      const existingConfig = await readJsonFile(configPath, {})

      const updatedConfig = {
        ...existingConfig,
        ...config,
        language: "pt", // Force Portuguese
        database_type: "json",
        updated_at: new Date().toISOString()
      }

      // Ensure we have an ID
      if (!updatedConfig.id) {
        updatedConfig.id = 1
        updatedConfig.created_at = new Date().toISOString()
      }

      await writeJsonFile(configPath, updatedConfig)
    }
  } catch (error) {
    console.error("Error saving config:", error)
    throw error
  }
}

export async function saveMindMap(mindMap: any): Promise<string> {
    await initializeDatabase();
    const now = new Date();

    if ('id' in mindMap && mindMap.id) {
        // Update existing mind map
        try {
            if (dbType === "neon") {
                await db('UPDATE mind_maps SET name = $1, description = $2, data = $3, is_protected = $4, password_hash = $5, updated_at = $6 WHERE id = $7', [mindMap.name, mindMap.description, JSON.stringify(mindMap.data), mindMap.is_protected || false, mindMap.password_hash, now, mindMap.id]);
            } else {
                // JSON file database
                const mindMaps = await readJsonFile(mindMapsPath, []);
                const index = mindMaps.findIndex((m: any) => m.id === mindMap.id);
                if (index !== -1) {
                    mindMaps[index] = {
                        ...mindMaps[index],
                        name: mindMap.name,
                        description: mindMap.description,
                        data: mindMap.data,
                        user_id: mindMap.user_id,
                        is_protected: mindMap.is_protected || false,
                        password_hash: mindMap.password_hash,
                        updated_at: now.toISOString()
                    };
                    await writeJsonFile(mindMapsPath, mindMaps);
                }
            }
            return mindMap.id as string;
        } catch (error) {
            console.error("Error updating mind map:", error);
            throw error;
        }
    } else {
        // Create new mind map
        const id = crypto.randomUUID();
        try {
            if (dbType === "neon") {
                await db('INSERT INTO mind_maps (id, name, description, data, user_id, is_protected, password_hash, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)', [id, mindMap.name, mindMap.description, JSON.stringify(mindMap.data), mindMap.user_id, mindMap.is_protected || false, mindMap.password_hash, now, now]);
            } else {
                // JSON file database
                const mindMaps = await readJsonFile(mindMapsPath, []);
                const newMindMap = {
                    id,
                    name: mindMap.name,
                    description: mindMap.description,
                    data: mindMap.data,
                    user_id: mindMap.user_id,
                    is_protected: mindMap.is_protected || false,
                    password_hash: mindMap.password_hash,
                    created_at: now.toISOString(),
                    updated_at: now.toISOString()
                };
                mindMaps.push(newMindMap);
                await writeJsonFile(mindMapsPath, mindMaps);
            }
            return id;
        } catch (error) {
            console.error("Error saving new mind map:", error);
            throw error;
        }
    }
}

export async function getMindMaps(userId?: string): Promise<MindMap[]> {
  await initializeDatabase()

  try {
    let mindMaps

    if (dbType === "neon") {
      if (userId) {
        mindMaps = await db("SELECT * FROM mind_maps WHERE user_id = $1 ORDER BY created_at DESC", [userId])
      } else {
        mindMaps = await db("SELECT * FROM mind_maps ORDER BY created_at DESC")
      }
    } else {
      // JSON file database
      mindMaps = await readJsonFile(mindMapsPath, [])

      if (userId) {
        mindMaps = mindMaps.filter((m: any) => m.user_id === userId)
      }

      // Sort by created_at DESC
      mindMaps.sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    }

    return mindMaps.map((row: any) => ({
      ...row,
      data: typeof row.data === "string" ? JSON.parse(row.data) : row.data,
      created_at: new Date(row.created_at),
      updated_at: new Date(row.updated_at),
    }))
  } catch (error) {
    console.error("Error getting mind maps:", error)
    return []
  }
}

export async function getMindMapById(id: string): Promise<MindMap | null> {
  await initializeDatabase()

  try {
    let mindMap

    if (dbType === "neon") {
      const result = await db("SELECT * FROM mind_maps WHERE id = $1", [id])
      mindMap = result[0]
    } else {
      // JSON file database
      const mindMaps = await readJsonFile(mindMapsPath, [])
      mindMap = mindMaps.find((m: any) => m.id === id)
    }

    if (!mindMap) {
      return null
    }

    return {
      ...mindMap,
      data: typeof mindMap.data === "string" ? JSON.parse(mindMap.data) : mindMap.data,
      created_at: new Date(mindMap.created_at),
      updated_at: new Date(mindMap.updated_at),
    }
  } catch (error) {
    console.error(`Error getting mind map with id ${id}:`, error)
    return null
  }
}

export async function deleteMindMap(id: string): Promise<void> {
  await initializeDatabase()

  try {
    if (dbType === "neon") {
      await db("DELETE FROM mind_maps WHERE id = $1", [id])
    } else {
      // JSON file database
      const mindMaps = await readJsonFile(mindMapsPath, [])
      const filteredMindMaps = mindMaps.filter((m: any) => m.id !== id)
      await writeJsonFile(mindMapsPath, filteredMindMaps)
    }
  } catch (error) {
    console.error("Error deleting mind map:", error)
    throw error
  }
}

export async function clearAllMindMaps(): Promise<void> {
  await initializeDatabase()

  try {
    if (dbType === "neon") {
      await db("DELETE FROM mind_maps")
    } else {
      // JSON file database
      await writeJsonFile(mindMapsPath, [])
    }
  } catch (error) {
    console.error("Error clearing mind maps:", error)
    throw error
  }
}

// ===== USER MANAGEMENT FUNCTIONS =====

const usersPath = join(process.cwd(), "data", "users.json")

export async function createUser(email: string, name: string, password: string): Promise<string> {
  await initializeDatabase()

  try {
    const hashedPassword = await bcrypt.hash(password, 12)
    const userId = generateId()

    if (dbType === "neon") {
      // For Neon database
      await db(`
        INSERT INTO users (id, email, name, password_hash, created_at, updated_at)
        VALUES ($1, $2, $3, $4, NOW(), NOW())
      `, [userId, email, name, hashedPassword])
    } else {
      // JSON file database
      const users = await readJsonFile(usersPath, [])

      // Check if user already exists
      if (users.find((u: any) => u.email === email)) {
        throw new Error("User already exists")
      }

      const newUser = {
        id: userId,
        email,
        name,
        password_hash: hashedPassword,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      users.push(newUser)
      await writeJsonFile(usersPath, users)
    }

    return userId
  } catch (error) {
    console.error("Error creating user:", error)
    throw error
  }
}

export async function getUserByEmail(email: string): Promise<User | null> {
  await initializeDatabase()

  try {
    if (dbType === "neon") {
      const result = await db("SELECT * FROM users WHERE email = $1", [email])
      return result[0] || null
    } else {
      // JSON file database
      const users = await readJsonFile(usersPath, [])
      return users.find((u: any) => u.email === email) || null
    }
  } catch (error) {
    console.error("Error getting user by email:", error)
    return null
  }
}

export async function getUserById(id: string): Promise<User | null> {
  await initializeDatabase()

  try {
    if (dbType === "neon") {
      const result = await db("SELECT * FROM users WHERE id = $1", [id])
      return result[0] || null
    } else {
      // JSON file database
      const users = await readJsonFile(usersPath, [])
      return users.find((u: any) => u.id === id) || null
    }
  } catch (error) {
    console.error("Error getting user by id:", error)
    return null
  }
}

export async function verifyPassword(email: string, password: string): Promise<User | null> {
  const user = await getUserByEmail(email)
  if (!user) return null

  const isValid = await bcrypt.compare(password, user.password_hash)
  return isValid ? user : null
}

// ===== SHARED MIND MAPS FUNCTIONS =====

const sharedMindMapsPath = join(process.cwd(), "data", "shared_mindmaps.json")

export async function shareMindMap(
  mindmapId: string,
  ownerId: string,
  sharedWithEmail: string,
  permission: 'view' | 'edit' = 'view'
): Promise<string> {
  await initializeDatabase()

  try {
    // Check if the user exists
    const sharedWithUser = await getUserByEmail(sharedWithEmail)
    if (!sharedWithUser) {
      throw new Error("User not found")
    }

    // Check if already shared
    const existingShares = await readJsonFile(sharedMindMapsPath, [])
    const existingShare = existingShares.find((s: any) =>
      s.mindmap_id === mindmapId && s.shared_with_id === sharedWithUser.id
    )

    if (existingShare) {
      throw new Error("Mind map already shared with this user")
    }

    const shareId = generateId()
    const newShare = {
      id: shareId,
      mindmap_id: mindmapId,
      owner_id: ownerId,
      shared_with_id: sharedWithUser.id,
      shared_with_email: sharedWithEmail,
      permission,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    if (dbType === "neon") {
      await db(`
        INSERT INTO shared_mindmaps (id, mindmap_id, owner_id, shared_with_id, shared_with_email, permission, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
      `, [shareId, mindmapId, ownerId, sharedWithUser.id, sharedWithEmail, permission])
    } else {
      // JSON file database
      existingShares.push(newShare)
      await writeJsonFile(sharedMindMapsPath, existingShares)
    }

    return shareId
  } catch (error) {
    console.error("Error sharing mind map:", error)
    throw error
  }
}

export async function getSharedMindMaps(userId: string): Promise<any[]> {
  await initializeDatabase()

  try {
    if (dbType === "neon") {
      const shares = await db(`
        SELECT sm.*, mm.name, mm.description, mm.data, mm.created_at as mindmap_created_at, mm.updated_at as mindmap_updated_at,
               u.name as owner_name, u.email as owner_email
        FROM shared_mindmaps sm
        JOIN mind_maps mm ON sm.mindmap_id = mm.id
        JOIN users u ON sm.owner_id = u.id
        WHERE sm.shared_with_id = $1
        ORDER BY sm.created_at DESC
      `, [userId])
      return shares
    } else {
      // JSON file database
      const shares = await readJsonFile(sharedMindMapsPath, [])
      const mindMaps = await readJsonFile(mindMapsPath, [])
      const users = await readJsonFile(usersPath, [])

      const userShares = shares.filter((s: any) => s.shared_with_id === userId)

      return userShares.map((share: any) => {
        const mindMap = mindMaps.find((m: any) => m.id === share.mindmap_id)
        const owner = users.find((u: any) => u.id === share.owner_id)

        return {
          ...share,
          name: mindMap?.name,
          description: mindMap?.description,
          data: mindMap?.data,
          mindmap_created_at: mindMap?.created_at,
          mindmap_updated_at: mindMap?.updated_at,
          owner_name: owner?.name,
          owner_email: owner?.email
        }
      })
    }
  } catch (error) {
    console.error("Error getting shared mind maps:", error)
    return []
  }
}

export async function removeShare(shareId: string, userId: string): Promise<void> {
  await initializeDatabase()

  try {
    if (dbType === "neon") {
      await db(`
        DELETE FROM shared_mindmaps
        WHERE id = $1 AND (owner_id = $2 OR shared_with_id = $2)
      `, [shareId, userId])
    } else {
      // JSON file database
      const shares = await readJsonFile(sharedMindMapsPath, [])
      const filteredShares = shares.filter((s: any) =>
        !(s.id === shareId && (s.owner_id === userId || s.shared_with_id === userId))
      )
      await writeJsonFile(sharedMindMapsPath, filteredShares)
    }
  } catch (error) {
    console.error("Error removing share:", error)
    throw error
  }
}

export async function getMindMapShares(mindmapId: string, ownerId: string): Promise<any[]> {
  await initializeDatabase()

  try {
    if (dbType === "neon") {
      const shares = await db(`
        SELECT sm.*, u.name as shared_with_name, u.email as shared_with_email
        FROM shared_mindmaps sm
        JOIN users u ON sm.shared_with_id = u.id
        WHERE sm.mindmap_id = $1 AND sm.owner_id = $2
        ORDER BY sm.created_at DESC
      `, [mindmapId, ownerId])
      return shares
    } else {
      // JSON file database
      const shares = await readJsonFile(sharedMindMapsPath, [])
      const users = await readJsonFile(usersPath, [])

      const mindmapShares = shares.filter((s: any) =>
        s.mindmap_id === mindmapId && s.owner_id === ownerId
      )

      return mindmapShares.map((share: any) => {
        const user = users.find((u: any) => u.id === share.shared_with_id)
        return {
          ...share,
          shared_with_name: user?.name,
          shared_with_email: user?.email
        }
      })
    }
  } catch (error) {
    console.error("Error getting mind map shares:", error)
    return []
  }
}

// ===== PASSWORD PROTECTION FUNCTIONS =====

export async function setMindMapPassword(mindmapId: string, password: string, userId: string): Promise<void> {
  await initializeDatabase()

  try {
    const passwordHash = await bcrypt.hash(password, 12)

    if (dbType === "neon") {
      await db(`
        UPDATE mind_maps
        SET is_protected = true, password_hash = $1, updated_at = NOW()
        WHERE id = $2 AND user_id = $3
      `, [passwordHash, mindmapId, userId])
    } else {
      // JSON file database
      const mindMaps = await readJsonFile(mindMapsPath, [])
      const index = mindMaps.findIndex((m: any) => m.id === mindmapId && m.user_id === userId)

      if (index !== -1) {
        mindMaps[index] = {
          ...mindMaps[index],
          is_protected: true,
          password_hash: passwordHash,
          updated_at: new Date().toISOString()
        }
        await writeJsonFile(mindMapsPath, mindMaps)
      }
    }
  } catch (error) {
    console.error("Error setting mind map password:", error)
    throw error
  }
}

export async function removeMindMapPassword(mindmapId: string, userId: string): Promise<void> {
  await initializeDatabase()

  try {
    if (dbType === "neon") {
      await db(`
        UPDATE mind_maps
        SET is_protected = false, password_hash = NULL, updated_at = NOW()
        WHERE id = $1 AND user_id = $2
      `, [mindmapId, userId])
    } else {
      // JSON file database
      const mindMaps = await readJsonFile(mindMapsPath, [])
      const index = mindMaps.findIndex((m: any) => m.id === mindmapId && m.user_id === userId)

      if (index !== -1) {
        mindMaps[index] = {
          ...mindMaps[index],
          is_protected: false,
          password_hash: undefined,
          updated_at: new Date().toISOString()
        }
        await writeJsonFile(mindMapsPath, mindMaps)
      }
    }
  } catch (error) {
    console.error("Error removing mind map password:", error)
    throw error
  }
}

export async function verifyMindMapPassword(mindmapId: string, password: string): Promise<boolean> {
  await initializeDatabase()

  try {
    let mindMap

    if (dbType === "neon") {
      const result = await db(`
        SELECT password_hash FROM mind_maps
        WHERE id = $1 AND is_protected = true
      `, [mindmapId])
      mindMap = result[0]
    } else {
      // JSON file database
      const mindMaps = await readJsonFile(mindMapsPath, [])
      mindMap = mindMaps.find((m: any) => m.id === mindmapId && m.is_protected)
    }

    if (!mindMap || !mindMap.password_hash) {
      return false
    }

    return await bcrypt.compare(password, mindMap.password_hash)
  } catch (error) {
    console.error("Error verifying mind map password:", error)
    return false
  }
}
