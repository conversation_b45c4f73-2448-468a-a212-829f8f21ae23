import { neon } from "@neondatabase/serverless"
import Database from "better-sqlite3"
import { join } from "path"

export interface Config {
  id?: number
  language: string
  database_type: "sqlite" | "neon"
  database_url?: string
  llm_provider: "openrouter" | "ollama"
  openrouter_api_key?: string
  ollama_base_url?: string
  default_model?: string
  setup_completed?: boolean
  created_at?: Date
  updated_at?: Date
}

export interface MindMap {
  id?: string
  name: string
  description?: string
  data: any
  created_at: Date
  updated_at: Date
}

export let db: any = null
let dbType: "sqlite" | "neon" = "sqlite"

export async function initializeDatabase() {
  if (db) return db

  // Check if we have a Neon database URL
  const databaseUrl = process.env.DATABASE_URL

  if (databaseUrl && databaseUrl.includes("neon.tech")) {
    // Use Neon database
    dbType = "neon"
    db = neon(databaseUrl)

    // Initialize tables for Neon
    await db(`
      CREATE TABLE IF NOT EXISTS config (
        id SERIAL PRIMARY KEY,
        language VARCHAR(10) DEFAULT 'pt',
        database_type VARCHAR(20) DEFAULT 'neon',
        database_url TEXT,
        llm_provider VARCHAR(20) DEFAULT 'openrouter',
        openrouter_api_key TEXT,
        ollama_base_url TEXT DEFAULT 'http://localhost:11434',
        default_model VARCHAR(100) DEFAULT 'openai/gpt-4o-mini',
        setup_completed BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    await db(`
      CREATE TABLE IF NOT EXISTS mind_maps (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        data JSONB NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Add setup_completed column if it doesn't exist for Neon (ignore errors)
    try {
      await db(`ALTER TABLE config ADD COLUMN setup_completed BOOLEAN DEFAULT FALSE`);
    } catch (e) { /* ignore */ }

  } else {
    // Use SQLite database
    dbType = "sqlite"
    const dbPath = join(process.cwd(), "mindmap.db")
    db = new Database(dbPath)

    // Initialize tables for SQLite
    db.exec(`
      CREATE TABLE IF NOT EXISTS config (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        language TEXT DEFAULT 'pt',
        database_type TEXT DEFAULT 'sqlite',
        database_url TEXT,
        llm_provider TEXT DEFAULT 'openrouter',
        openrouter_api_key TEXT,
        ollama_base_url TEXT DEFAULT 'http://localhost:11434',
        default_model TEXT DEFAULT 'openai/gpt-4o-mini',
        setup_completed BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    db.exec(`
      CREATE TABLE IF NOT EXISTS mind_maps (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        data TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Add setup_completed column if it doesn't exist for SQLite (ignore errors)
    try {
      db.exec(`ALTER TABLE config ADD COLUMN setup_completed BOOLEAN DEFAULT FALSE`);
    } catch (e) { /* ignore */ }
    
  }

  // Ensure default config exists with Portuguese
  await ensureDefaultConfig()

  return db
}
console.log("Initialized db object:", db);

async function ensureDefaultConfig() {
  try {
    let existingConfig

    if (dbType === "neon") {
      const result = await db("SELECT * FROM config LIMIT 1")
      existingConfig = result[0]
    } else {
      existingConfig = db.prepare("SELECT * FROM config LIMIT 1").get()
    }

    if (!existingConfig) {
      const defaultConfig = {
        language: "pt",
        database_type: dbType,
        database_url: process.env.DATABASE_URL || null,
        llm_provider: "openrouter",
        openrouter_api_key: null,
        ollama_base_url: "http://localhost:11434",
        default_model: "openai/gpt-4o-mini",
      }

      if (dbType === "neon") {
        await db(
          `
          INSERT INTO config (language, database_type, database_url, llm_provider, openrouter_api_key, ollama_base_url, default_model)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
        `,
          [
            defaultConfig.language,
            defaultConfig.database_type,
            defaultConfig.database_url,
            defaultConfig.llm_provider,
            defaultConfig.openrouter_api_key,
            defaultConfig.ollama_base_url,
            defaultConfig.default_model,
          ],
        )
      } else {
        db.prepare(`
          INSERT INTO config (language, database_type, database_url, llm_provider, openrouter_api_key, ollama_base_url, default_model)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `).run(
          defaultConfig.language,
          defaultConfig.database_type,
          defaultConfig.database_url,
          defaultConfig.llm_provider,
          defaultConfig.openrouter_api_key,
          defaultConfig.ollama_base_url,
          defaultConfig.default_model,
        )
      }
    } else if (existingConfig.language !== "pt") {
      // Force Portuguese if not set
      if (dbType === "neon") {
        await db("UPDATE config SET language = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2", [
          "pt",
          existingConfig.id,
        ])
      } else {
        db.prepare("UPDATE config SET language = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?").run(
          "pt",
          existingConfig.id,
        )
      }
    }
  } catch (error) {
    console.error("Error ensuring default config:", error)
  }
}

export async function getDatabase() {
  return await initializeDatabase()
}

export async function getConfig(): Promise<Config> {
  const database = await getDatabase()

  try {
    let config

    if (dbType === "neon") {
      const result = await database("SELECT * FROM config LIMIT 1")
      config = result[0]
    } else {
      config = database.prepare("SELECT * FROM config LIMIT 1").get()
    }

    if (!config) {
      await ensureDefaultConfig()
      if (dbType === "neon") {
        const result = await database("SELECT * FROM config LIMIT 1")
        config = result[0]
      } else {
        config = database.prepare("SELECT * FROM config LIMIT 1").get()
      }
    }

    // Ensure Portuguese is set
    if (config.language !== "pt") {
      config.language = "pt"
      await saveConfig(config)
    }

    return config
  } catch (error) {
    console.error("Error getting config:", error)
    // Return default config with Portuguese
    return {
      language: "pt",
      database_type: dbType,
      llm_provider: "openrouter",
      ollama_base_url: "http://localhost:11434",
      default_model: "openai/gpt-4o-mini",
    }
  }
}

export async function saveConfig(config: Partial<Config>): Promise<void> {
  const database = await getDatabase()

  try {
    // Force Portuguese
    config.language = "pt"

    if (dbType === "neon") {
      const existing = await database("SELECT id FROM config LIMIT 1")

      if (existing.length > 0) {
        await database(
          `
          UPDATE config SET 
            language = $1,
            database_type = $2,
            database_url = $3,
            llm_provider = $4,
            openrouter_api_key = $5,
            ollama_base_url = $6,
            default_model = $7,
            setup_completed = $8,
            updated_at = CURRENT_TIMESTAMP
          WHERE id = $9
        `,
          [
            config.language || "pt",
            config.database_type || "neon",
            config.database_url,
            config.llm_provider || "openrouter",
            config.openrouter_api_key,
            config.ollama_base_url || "http://localhost:11434",
            config.default_model || "openai/gpt-4o-mini",
            config.setup_completed === true, // Ensure boolean
            existing[0].id,
          ],
        )
      } else {
        await database(
          `
          INSERT INTO config (language, database_type, database_url, llm_provider, openrouter_api_key, ollama_base_url, default_model, setup_completed)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        `,
          [
            config.language || "pt",
            config.database_type || "neon",
            config.database_url,
            config.llm_provider || "openrouter",
            config.openrouter_api_key,
            config.ollama_base_url || "http://localhost:11434",
            config.default_model || "openai/gpt-4o-mini",
            config.setup_completed === true, // Ensure boolean
          ],
        )
      }
    } else {
      const existing = database.prepare("SELECT id FROM config LIMIT 1").get()

      if (existing) {
        database
          .prepare(
            `
          UPDATE config SET
            language = ?,
            database_type = ?,
            database_url = ?,
            llm_provider = ?,
            openrouter_api_key = ?,
            ollama_base_url = ?,
            default_model = ?,
            setup_completed = ?,
            updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `,
          )
          .run(
            config.language || "pt",
            config.database_type || "sqlite",
            config.database_url,
            config.llm_provider || "openrouter",
            config.openrouter_api_key,
            config.ollama_base_url || "http://localhost:11434",
            config.default_model || "openai/gpt-4o-mini",
            config.setup_completed ? 1 : 0, // Ensure 1 or 0 for SQLite
            existing.id,
          )
      } else {
        database
          .prepare(
            `
          INSERT INTO config (language, database_type, database_url, llm_provider, openrouter_api_key, ollama_base_url, default_model, setup_completed)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `,
          )
          .run(
            config.language || "pt",
            config.database_type || "sqlite",
            config.database_url,
            config.llm_provider || "openrouter",
            config.openrouter_api_key,
            config.ollama_base_url || "http://localhost:11434",
            config.default_model || "openai/gpt-4o-mini",
            config.setup_completed ? 1 : 0, // Ensure 1 or 0 for SQLite
          )
      }
    }
  } catch (error) {
    console.error("Error saving config:", error)
    throw error
  }
}

export async function saveMindMap(mindMap: Omit<MindMap, "created_at" | "updated_at">): Promise<string> {
    const db = await getDatabase();
    const now = new Date();

    if ('id' in mindMap && mindMap.id) {
        // Update existing mind map
        try {
            if (dbType === "neon") {
                await db('UPDATE mind_maps SET name = $1, description = $2, data = $3, updated_at = $4 WHERE id = $5', [mindMap.name, mindMap.description, JSON.stringify(mindMap.data), now, mindMap.id]);
            } else {
                db.prepare('UPDATE mind_maps SET name = ?, description = ?, data = ?, updated_at = ? WHERE id = ?').run(mindMap.name, mindMap.description, JSON.stringify(mindMap.data), now.toISOString(), mindMap.id);
            }
            return mindMap.id as string;
        } catch (error) {
            console.error("Error updating mind map:", error);
            throw error;
        }
    } else {
        // Create new mind map
        const id = crypto.randomUUID();
        try {
            if (dbType === "neon") {
                await db('INSERT INTO mind_maps (id, name, description, data, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6)', [id, mindMap.name, mindMap.description, JSON.stringify(mindMap.data), now, now]);
            } else {
                db.prepare('INSERT INTO mind_maps (id, name, description, data, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)').run(id, mindMap.name, mindMap.description, JSON.stringify(mindMap.data), now.toISOString(), now.toISOString());
            }
            return id;
        } catch (error) {
            console.error("Error saving new mind map:", error);
            throw error;
        }
    }
}

export async function getMindMaps(): Promise<MindMap[]> {
  const database = await getDatabase()

  try {
    let mindMaps

    if (dbType === "neon") {
      mindMaps = await database("SELECT * FROM mind_maps ORDER BY created_at DESC")
    } else {
      mindMaps = database.prepare("SELECT * FROM mind_maps ORDER BY created_at DESC").all()
    }

    return mindMaps.map((row: any) => ({
      ...row,
      data: typeof row.data === "string" ? JSON.parse(row.data) : row.data,
      created_at: new Date(row.created_at),
      updated_at: new Date(row.updated_at),
    }))
  } catch (error) {
    console.error("Error getting mind maps:", error)
    return []
  }
}

export async function getMindMapById(id: string): Promise<MindMap | null> {
  const database = await getDatabase()

  try {
    let mindMap

    if (dbType === "neon") {
      const result = await database("SELECT * FROM mind_maps WHERE id = $1", [id])
      mindMap = result[0]
    } else {
      mindMap = database.prepare("SELECT * FROM mind_maps WHERE id = ?").get(id)
    }

    if (!mindMap) {
      return null
    }

    return {
      ...mindMap,
      data: typeof mindMap.data === "string" ? JSON.parse(mindMap.data) : mindMap.data,
      created_at: new Date(mindMap.created_at),
      updated_at: new Date(mindMap.updated_at),
    }
  } catch (error) {
    console.error(`Error getting mind map with id ${id}:`, error)
    return null
  }
}
export async function deleteMindMap(id: string): Promise<void> {
  const database = await getDatabase()

  try {
    if (dbType === "neon") {
      await database("DELETE FROM mind_maps WHERE id = $1", [id])
    } else {
      database.prepare("DELETE FROM mind_maps WHERE id = ?").run(id)
    }
  } catch (error) {
    console.error("Error deleting mind map:", error)
    throw error
  }
}

export async function clearAllMindMaps(): Promise<void> {
  const database = await getDatabase()

  try {
    if (dbType === "neon") {
      await database("DELETE FROM mind_maps")
    } else {
      database.prepare("DELETE FROM mind_maps").run()
    }
  } catch (error) {
    console.error("Error clearing mind maps:", error)
    throw error
  }
}
