"use client"

import { useEffect, useState } from "react"
import { MainApp } from "@/components/main-app"
import { SetupWizard } from "@/components/setup-wizard"
import { setLanguage } from "@/lib/i18n"
import { Loader2 } from "lucide-react"

export default function Home() {
  type Status = "loading" | "needsConfiguration" | "configured"
  const [status, setStatus] = useState<Status>("loading")

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        const response = await fetch("/api/config")
        if (response.ok) {
          const config = await response.json()
          if (config.setup_completed) {
            setStatus("configured")
          } else {
            setStatus("needsConfiguration")
          }
        } else {
          setStatus("needsConfiguration")
        }
      } catch (error) {
        console.error("Failed to fetch config:", error)
        setStatus("needsConfiguration")
      }
    }

    fetchConfig()
  }, [])

  const handleSetupComplete = () => {
    setStatus("configured")
  }

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Carregando Mind Map IA...</p>
        </div>
      </div>
    )
  }

  if (status === "needsConfiguration") {
    return <SetupWizard onComplete={handleSetupComplete} />
  }

  if (status === "configured") {
    return <MainApp />
  }

  return null
}
