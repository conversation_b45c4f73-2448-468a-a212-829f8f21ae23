"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import type { MindMapNode as Node } from "@/types/mindmap"
import { Input } from "@/components/ui/input"
import { X, Edit3, <PERSON>, <PERSON>rk<PERSON> } from "lucide-react"
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu"
import useMindMapStore from "@/lib/store"

interface MindMapNodeProps {
  node: Node
  isSelected: boolean
  isConnecting: boolean
  onClick: () => void
  onDragStart: (e: React.MouseEvent) => void
  onDelete: () => void
  onConnectionStart: () => void
  onConnectionEnd: () => void
  onResizeStart: (e: React.MouseEvent) => void
}

export function MindMapNode({
  node,
  isSelected,
  isConnecting,
  onClick,
  onDragStart,
  onDelete,
  onConnectionStart,
  onConnectionEnd,
  onResizeStart,
}: MindMapNodeProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editContent, setEditContent] = useState(node.content)
  const inputRef = useRef<HTMLInputElement>(null)
  const { updateNode, addChildNode, addSiblingNode, deleteNode: deleteNodeFromStore } = useMindMapStore()
  const { setIconRequest } = useMindMapStore()
  const [style, setStyle] = useState<"filled" | "outline">(node.style ?? "filled");

  const colors = [
    "#3b82f6", // azul
    "#ef4444", // vermelho
    "#10b981", // verde
    "#f59e0b", // amarelo
    "#8b5cf6", // roxo
    "#ec4899", // rosa
    "#06b6d4", // ciano
    "#84cc16", // lima
    "#f97316", // laranja
    "#6366f1", // índigo
    "#14b8a6", // teal
    "#a855f7"  // violeta
  ]

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
      inputRef.current.select()
    }
  }, [isEditing])

  const handleEdit = () => {
    setIsEditing(true)
    setEditContent(node.content)
  }

  const handleSaveEdit = () => {
    if (editContent.trim()) {
      updateNode(node.id, { content: editContent.trim() })
    }
    setIsEditing(false)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSaveEdit()
    } else if (e.key === "Escape") {
      setIsEditing(false)
      setEditContent(node.content)
    }
  }

  const nodeSize = {
    small: { width: 120, height: 60 },
    medium: { width: 160, height: 80 },
    large: { width: 200, height: 100 },
  }

  const size = nodeSize[node.size ?? "medium"]

  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>
        <g>
          {/* Fundo do Nó */}
          <rect
            x={node.x - size.width / 2}
            y={node.y - size.height / 2}
            width={size.width}
            height={size.height}
            rx="12"
            fill={style === "filled" ? node.color : "transparent"}
            stroke={isSelected ? "#1f2937" : "transparent"}
            strokeWidth={isSelected ? "3" : "0"}
            className="cursor-pointer drop-shadow-md"
            onMouseDown={onDragStart}
            onClick={onClick}
            onDoubleClick={handleEdit}
            onMouseEnter={isConnecting ? onConnectionEnd : undefined}
          />

          {/* Conteúdo do Nó */}
          {isEditing ? (
            <foreignObject x={node.x - size.width / 2 + 8} y={node.y - 10} width={size.width - 16} height={20}>
              <Input
                ref={inputRef}
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                onBlur={handleSaveEdit}
                onKeyDown={handleKeyPress}
                className="text-xs h-5 px-1 bg-white/90"
              />
            </foreignObject>
          ) : (
            <>
              <text
                x={node.x}
                y={node.y}
                textAnchor="middle"
                dominantBaseline="middle"
                fill={style === "filled" ? "white" : "#374151"}
                fontSize="12"
                fontWeight="500"
                className="pointer-events-none select-none"
              >
                {node.icon && !(node.icon.startsWith("http") || node.icon.startsWith("data:")) ? `${node.icon} ` : ""}
                {(node.content ?? "").length > 18 ? `${(node.content ?? "").substring(0, 18)}...` : (node.content ?? "")}
              </text>
              {node.icon && (node.icon.startsWith("http") || node.icon.startsWith("data:")) && (
                <image
                  href={node.icon}
                  x={node.x - 8}
                  y={node.y - 8}
                  width="16"
                  height="16"
                />
              )}
            </>
          )}

      {/* Controles do Nó (visível quando selecionado) */}
      {isSelected && !isEditing && (
        <g>
          {/* Botão Editar */}
          <circle
            cx={node.x + size.width / 2 - 15}
            cy={node.y - size.height / 2 + 15}
            r="12"
            fill="white"
            stroke="#e5e7eb"
            strokeWidth="1"
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation()
              handleEdit()
            }}
          />
          <Edit3
            x={node.x + size.width / 2 - 21}
            y={node.y - size.height / 2 + 9}
            width="12"
            height="12"
            className="pointer-events-none"
          />

          {/* Botão Conectar */}
          <circle
            cx={node.x + size.width / 2 - 15}
            cy={node.y}
            r="12"
            fill="white"
            stroke="#e5e7eb"
            strokeWidth="1"
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation()
              onConnectionStart()
            }}
          />
          <Link
            x={node.x + size.width / 2 - 21}
            y={node.y - 6}
            width="12"
            height="12"
            className="pointer-events-none"
          />

          {/* Botão Alterar Ícone */}
          <circle
            cx={node.x + size.width / 2 - 15}
            cy={node.y + size.height / 2 - 15}
            r="12"
            fill="white"
            stroke="#e5e7eb"
            strokeWidth="1"
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation()
              // Dispara fluxo de sugestão de ícone via chat
              setIconRequest({ nodeId: node.id, prompt: `Sugira conexões conceituais profundas e relevantes com outros nós deste mapa mental, com base no conteúdo deste nó: ${node.content}` })
            }}
          />
          <Sparkles
            x={node.x + size.width / 2 - 21}
            y={node.y + size.height / 2 - 21}
            width="12"
            height="12"
            className="pointer-events-none"
          />

          {/* Botão Deletar */}
          <circle
            cx={node.x + size.width / 2 - 15}
            cy={node.y + size.height / 2 + 15}
            r="12"
            fill="white"
            stroke="#e5e7eb"
            strokeWidth="1"
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation()
              onDelete()
            }}
          />
          <X
            x={node.x + size.width / 2 - 21}
            y={node.y + size.height / 2 + 9}
            width="12"
            height="12"
            className="pointer-events-none text-red-500"
          />

          {/* Opção de estilo pontilhado */}
          <rect
            x={node.x - size.width / 2 + 15}
            y={node.y + size.height / 2 + 25}
            width="16"
            height="16"
            fill="transparent"
            stroke="#6b7280"
            strokeWidth="2"
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation()
              const newStyle = style === "filled" ? "outline" : "filled"
              setStyle(newStyle)
              updateNode(node.id, { style: newStyle })
            }}
          />

          {/* Paleta de Cores */}
          {colors.map((color, index) => {
            const row = Math.floor(index / 6);
            const col = index % 6;
            return (
              <circle
                key={color}
                cx={node.x - size.width / 2 + 40 + col * 18}
                cy={node.y + size.height / 2 + 25 + row * 20}
                r="7"
                fill={color}
                stroke={node.color === color ? "#1f2937" : "#e5e7eb"}
                strokeWidth={node.color === color ? "2" : "1"}
                className="cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation()
                  updateNode(node.id, { color })
                }}
              />
            );
          })}

          {/* Handle de resize */}
          <rect
            x={node.x + size.width / 2 - 8}
            y={node.y + size.height / 2 - 8}
            width={8}
            height={8}
            fill="white"
            stroke="#6b7280"
            strokeWidth="1"
            className="cursor-se-resize"
            onMouseDown={(e) => {
              e.stopPropagation()
              onResizeStart(e)
            }}
          />
        </g>
      )}
        </g>
      </ContextMenuTrigger>
      <ContextMenuContent>
        <ContextMenuItem onClick={() => addChildNode(node.id)}>Adicionar filho</ContextMenuItem>
        <ContextMenuItem onClick={() => addSiblingNode(node.id)}>Adicionar irmão</ContextMenuItem>
        <ContextMenuSeparator />
        <ContextMenuItem onClick={() => deleteNodeFromStore(node.id)}>Excluir nó</ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  )
}
