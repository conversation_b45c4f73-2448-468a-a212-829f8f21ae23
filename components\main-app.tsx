"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ConfigModal } from "@/components/config-modal"
import { HelpModal } from "@/components/help-modal"
import { MindMapEditor } from "@/components/mind-map-editor"
import { AuthModal } from "@/components/auth/auth-modal"
import { ShareModal } from "@/components/share-modal"
import { PasswordProtectionModal } from "@/components/password-protection-modal"
import { useAuth } from "@/components/auth/auth-provider"
import { Settings, Plus, Brain, Loader2, Search, Trash2, Calendar, FileText, Edit, Save, HelpCircle, User, LogOut, Share2, Users, Lock, Shield, ShieldOff } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useTranslation } from "@/lib/i18n"
import { MindMap } from "@/types/mindmap"
import useMindMapStore from "@/lib/store"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"

export function MainApp() {
  const { setMindMapId } = useMindMapStore()
  const { user, isLoading: authLoading, logout, login } = useAuth()
  const [mindMaps, setMindMaps] = useState<MindMap[]>([])
  const [sharedMindMaps, setSharedMindMaps] = useState<MindMap[]>([])
  const [currentView, setCurrentView] = useState<'my-maps' | 'shared-maps'>('my-maps')
  const [isConfigModalOpen, setIsConfigModalOpen] = useState(false)
  const [isHelpModalOpen, setIsHelpModalOpen] = useState(false)
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false)
  const [isShareModalOpen, setIsShareModalOpen] = useState(false)
  const [shareModalData, setShareModalData] = useState<{id: string, title: string} | null>(null)
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false)
  const [passwordModalData, setPasswordModalData] = useState<{id: string, title: string, mode: 'set' | 'unlock' | 'remove'} | null>(null)
  const [isSignupInviteOpen, setIsSignupInviteOpen] = useState(false)
  const [selectedMindMap, setSelectedMindMap] = useState<MindMap | null>(null)
  const [generating, setGenerating] = useState(false)
  const [isNewMap, setIsNewMap] = useState(false);
  const [prompt, setPrompt] = useState("")
  const [searchTerm, setSearchTerm] = useState("")
  const { toast } = useToast()
  const { t } = useTranslation()
  const [markdown,setMarkdown] = useState<string|null>(null)

  useEffect(() => {
    loadMindMaps()
    if (user) {
      loadSharedMindMaps()
    }
  }, [user])

  const loadMindMaps = async () => {
    try {
      const response = await fetch("/api/mindmaps")
      if (response.ok) {
        const data = await response.json()
        setMindMaps(data.mindMaps || [])
      }
    } catch (error) {
      console.error("Erro ao carregar mapas mentais:", error)
      toast({
        title: t.common.error,
        description: t.mainApp.loadError,
        variant: "destructive",
      })
    }
  }

  const loadSharedMindMaps = async () => {
    if (!user) return

    try {
      const response = await fetch("/api/shared-mindmaps")
      if (response.ok) {
        const data = await response.json()
        setSharedMindMaps(data.mindMaps || [])
      }
    } catch (error) {
      console.error("Erro ao carregar mapas compartilhados:", error)
      toast({
        title: t.common.error,
        description: "Erro ao carregar mapas compartilhados",
        variant: "destructive",
      })
    }
  }

  const handleShareClick = (mindMap: MindMap) => {
    setShareModalData({
      id: String(mindMap.id),
      title: mindMap.titulo
    })
    setIsShareModalOpen(true)
  }

  const handlePasswordClick = (mindMap: MindMap, mode: 'set' | 'unlock' | 'remove') => {
    setPasswordModalData({
      id: String(mindMap.id),
      title: mindMap.titulo,
      mode
    })
    setIsPasswordModalOpen(true)
  }

  const handlePasswordSuccess = (data?: any) => {
    if (passwordModalData?.mode === 'unlock' && data?.mindMap) {
      // If unlocking, open the mind map in editor
      setSelectedMindMap(data.mindMap)
      setMindMapId(String(data.mindMap.id))
    }
    // Refresh the mind maps list to update protection status
    loadMindMaps()
    if (user) {
      loadSharedMindMaps()
    }
  }

  const generateMindMap = async () => {
    if (!prompt.trim()) {
      toast({
        title: t.common.error,
        description: t.mainApp.topicRequired,
        variant: "destructive",
      })
      return
    }

    setGenerating(true)
    try {
      const response = await fetch("/api/chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          message: prompt,
          isGuest: !user // Indica se é usuário não logado
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Falha ao gerar mapa mental")
      }

      const data = await response.json()

      if (!data.success || !data.mindMap) {
        throw new Error(data.error || "Resposta inválida da API")
      }

      // Para usuários não logados, apenas mostrar o mapa sem salvar
      if (!user) {
        // Criar um mapa temporário para visualização
        const tempMindMap = {
          id: 'temp-preview',
          titulo: data.mindMap.name || prompt,
          dados: data.mindMap,
          criado_em: new Date().toISOString(),
          atualizado_em: new Date().toISOString()
        }

        setSelectedMindMap(tempMindMap)
        setMindMapId('temp-preview')
        setPrompt("")

        // Mostrar toast e modal convidando para se cadastrar
        toast({
          title: "🎉 Mapa gerado com sucesso!",
          description: "Experimente editar o mapa! Para salvar, faça login ou cadastre-se.",
          duration: 5000,
        })

        // Mostrar modal de convite após um pequeno delay
        setTimeout(() => {
          setIsSignupInviteOpen(true)
        }, 2000)

        return
      }

      // Para usuários logados, salvar normalmente
      const saveResponse = await fetch("/api/mindmaps", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          titulo: data.mindMap.name || prompt,
          dados: data.mindMap,
        }),
      })

      if (saveResponse.ok) {
        toast({
          title: t.common.success,
          description: t.mainApp.generateSuccess,
        })
        setPrompt("")
        const savedMap = await saveResponse.json()
        setMindMapId(String(savedMap.id))
        loadMindMaps()
      } else {
        throw new Error("Falha ao salvar mapa mental")
      }
    } catch (error) {
      console.error("Erro ao gerar mapa mental:", error)
      toast({
        title: t.common.error,
        description: error instanceof Error ? error.message : t.mainApp.generateError,
        variant: "destructive",
      })
    } finally {
      setGenerating(false)
    }
  }

  const deleteMindMap = async (id: string | number) => {
    if (!confirm("Tem certeza que deseja excluir este mapa mental?")) return

    try {
      const response = await fetch(`/api/mindmaps/${id}`, {
        method: "DELETE",
      })

      if (response.ok) {
        toast({
          title: t.common.success,
          description: t.mainApp.deleteSuccess,
        })
        loadMindMaps()
        if (selectedMindMap?.id === id) {
          setSelectedMindMap(null)
        }
      }
    } catch (error) {
      console.error("Erro ao excluir mapa mental:", error)
      toast({
        title: t.common.error,
        description: t.mainApp.deleteError,
        variant: "destructive",
      })
    }
  }

  const currentMindMaps = currentView === 'my-maps' ? mindMaps : sharedMindMaps
  const filteredMindMaps = currentMindMaps.filter(
    (mindMap) => mindMap.titulo && mindMap.titulo.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const createNewMindMap = async () => {
    try {
      const response = await fetch('/api/mindmaps', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          titulo: 'Novo Mapa Mental',
          dados: {
            nodes: [{ id: 'root', content: 'Tópico Central', x: 400, y: 300, level: 0 }],
            connections: [],
          },
        }),
      });

      if (!response.ok) {
        throw new Error('Falha ao criar novo mapa mental');
      }

      const newMindMap = await response.json();

      // Use the mind map object returned by the API
      const formattedMap = {
        id: newMindMap.mindMap.id,
        titulo: newMindMap.mindMap.titulo,
        dados: newMindMap.mindMap.dados,
        criado_em: newMindMap.mindMap.criado_em,
        atualizado_em: newMindMap.mindMap.atualizado_em
      };

      setSelectedMindMap(formattedMap);
      setMindMapId(String(formattedMap.id)); // Set the mind map ID in the store

    setIsNewMap(true); // Flag to indicate that this is a new map
      await loadMindMaps(); // Refresh the list of mind maps
    } catch (error) {
      console.error("Erro ao criar mapa mental:", error);
      toast({
        title: t.common.error,
        description: "Não foi possível criar o mapa mental.",
        variant: "destructive",
      });
    }
  };

  const handleEditClick = async (id: number | string) => {
    try {
      const response = await fetch(`/api/mindmaps/${id}`);
      if (response.ok) {
        const data = await response.json();

        // Check if the mind map is protected
        if (data.isProtected) {
          // Show password modal to unlock
          handlePasswordClick(data.mindMap, 'unlock');
          return;
        }

        setSelectedMindMap(data.mindMap);
        setMindMapId(String(id)); // Set the mind map ID in the store
      } else {
        toast({
          title: t.common.error,
          description: "Failed to load mind map details.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching mind map details:", error);
      toast({
        title: t.common.error,
        description: "Failed to load mind map details.",
        variant: "destructive",
      });
    }
  };

  if (selectedMindMap) {
    return (
      <MindMapEditor
        mindMap={selectedMindMap}
        onClose={() => {
        setSelectedMindMap(null);
        setIsNewMap(false); // Reset the flag when the editor is closed
        }}
        isNewMap={isNewMap}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Brain className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">{t.mainApp.title}</h1>
            </div>
            <div className="flex items-center gap-2">
              {user ? (
                <>
                  <div className="flex items-center space-x-2 mr-2">
                    <User className="h-4 w-4 text-gray-600" />
                    <span className="text-sm text-gray-700">{user.name}</span>
                  </div>
                  <Button variant="outline" size="sm" onClick={logout}>
                    <LogOut className="h-4 w-4 mr-2" />
                    Sair
                  </Button>
                </>
              ) : (
                <Button variant="outline" size="sm" onClick={() => setIsAuthModalOpen(true)}>
                  <User className="h-4 w-4 mr-2" />
                  Entrar
                </Button>
              )}
              <Button variant="outline" size="sm" onClick={() => setIsHelpModalOpen(true)}>
                <HelpCircle className="h-4 w-4 mr-2" />
                Ajuda
              </Button>
              {user?.isAdmin && (
                <Button variant="outline" size="sm" onClick={() => setIsConfigModalOpen(true)}>
                  <Settings className="h-4 w-4 mr-2" />
                  {t.mainApp.settings}
                </Button>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Mind Map Generator */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              {t.mainApp.createNew}
            </CardTitle>
            <CardDescription>{t.mainApp.createDescription}</CardDescription>
            {!user && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
                <div className="flex items-center justify-center gap-2 text-blue-800">
                  <Brain className="h-5 w-5" />
                  <span className="font-medium">Modo Demonstração</span>
                </div>
                <p className="text-sm text-blue-700 mt-2 text-center">
                  Experimente gratuitamente! Gere um mapa mental com até 8 nós.
                  <br />
                  <strong>Faça login ou cadastre-se</strong> para criar mapas ilimitados e salvá-los.
                </p>
              </div>
            )}
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Input
                placeholder={t.mainApp.topicPlaceholder}
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && !generating && generateMindMap()}
                className="flex-1"
              />
              <Button onClick={generateMindMap} disabled={generating || !prompt.trim()} size="lg">
                {generating ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    {t.mainApp.generating}
                  </>
                ) : (
                  <>
                    <Brain className="h-4 w-4 mr-2" />
                    {!user ? "Experimentar Grátis" : t.mainApp.generate}
                  </>
                )}
              </Button>
              <Button variant="outline" size="lg" onClick={createNewMindMap}>
                <Plus className="h-4 w-4 mr-2" />
                Novo Mapa
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Mind Maps Library */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <div className="flex items-center gap-4 mb-2">
                  <Button
                    variant={currentView === 'my-maps' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setCurrentView('my-maps')}
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Meus Mapas
                  </Button>
                  {user && (
                    <Button
                      variant={currentView === 'shared-maps' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setCurrentView('shared-maps')}
                    >
                      <Users className="h-4 w-4 mr-2" />
                      Compartilhados
                    </Button>
                  )}
                </div>
                <CardTitle>
                  {currentView === 'my-maps' ? t.mainApp.yourMindMaps : 'Mapas Compartilhados'}
                </CardTitle>
                <CardDescription>
                  {currentView === 'my-maps'
                    ? `${mindMaps.length} ${t.mainApp.mindMapsCount}`
                    : `${sharedMindMaps.length} mapas compartilhados com você`
                  }
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder={t.mainApp.searchPlaceholder}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-64"
                  />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {filteredMindMaps.length === 0 ? (
              <div className="text-center py-12">
                <Brain className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {currentMindMaps.length === 0
                    ? (currentView === 'my-maps' ? t.mainApp.noMindMaps : 'Nenhum mapa compartilhado')
                    : t.mainApp.noMatching
                  }
                </h3>
                <p className="text-gray-500 mb-4">
                  {currentMindMaps.length === 0
                    ? (currentView === 'my-maps'
                        ? t.mainApp.noMindMapsDescription
                        : 'Você ainda não tem mapas mentais compartilhados com você.'
                      )
                    : t.mainApp.noMatchingDescription
                  }
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredMindMaps.map((mindMap) => (
                  <Card key={mindMap.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                    <CardHeader className="pb-3">
                      <div className="flex justify-between items-start">
                        <div className="flex-1 min-w-0">
                          <CardTitle className="text-lg line-clamp-2 flex items-center gap-2">
                            {mindMap.titulo}
                            {(mindMap as any).is_protected && (
                              <Lock className="h-4 w-4 text-orange-600" />
                            )}
                          </CardTitle>
                          {currentView === 'shared-maps' && (mindMap as any).owner_name && (
                            <p className="text-sm text-gray-500 mt-1">
                              Compartilhado por {(mindMap as any).owner_name}
                            </p>
                          )}
                        </div>
                        <div className="flex flex-col items-end gap-1">
                          <Badge variant="secondary">
                            {mindMap.dados.nodes?.length || 0} {t.mainApp.nodes}
                          </Badge>
                          {currentView === 'shared-maps' && (
                            <Badge variant={(mindMap as any).permission === 'edit' ? 'default' : 'outline'} className="text-xs">
                              {(mindMap as any).permission === 'edit' ? 'Editar' : 'Ver'}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {mindMap.criado_em ? new Date(mindMap.criado_em).toLocaleDateString("pt-BR") : ''}
                        </div>
                        <div className="flex items-center gap-1">
                          <FileText className="h-4 w-4" />
                          Mapa Mental
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => mindMap.id && handleEditClick(mindMap.id)}
                          className="flex-1"
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          {currentView === 'shared-maps' && (mindMap as any).permission === 'view' ? 'Ver' : 'Editar'}
                        </Button>
                        {currentView === 'my-maps' && user && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleShareClick(mindMap)}
                            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                          >
                            <Share2 className="h-4 w-4" />
                          </Button>
                        )}
                        {currentView === 'my-maps' && user && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePasswordClick(mindMap, (mindMap as any).is_protected ? 'remove' : 'set')}
                            className={(mindMap as any).is_protected
                              ? "text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                              : "text-green-600 hover:text-green-700 hover:bg-green-50"
                            }
                            title={(mindMap as any).is_protected ? "Remover proteção por senha" : "Proteger com senha"}
                          >
                            {(mindMap as any).is_protected ? <ShieldOff className="h-4 w-4" /> : <Shield className="h-4 w-4" />}
                          </Button>
                        )}
                        {currentView === 'my-maps' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => mindMap.id && deleteMindMap(mindMap.id)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <ConfigModal isOpen={isConfigModalOpen} onClose={() => setIsConfigModalOpen(false)} />
      <HelpModal isOpen={isHelpModalOpen} onClose={() => setIsHelpModalOpen(false)} />
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        onSuccess={async () => {
          // Update auth state and refresh mind maps after login
          await login()
          loadMindMaps()
          loadSharedMindMaps()
        }}
      />
      {shareModalData && (
        <ShareModal
          isOpen={isShareModalOpen}
          onClose={() => {
            setIsShareModalOpen(false)
            setShareModalData(null)
          }}
          mindMapId={shareModalData.id}
          mindMapTitle={shareModalData.title}
        />
      )}
      {passwordModalData && (
        <PasswordProtectionModal
          isOpen={isPasswordModalOpen}
          onClose={() => {
            setIsPasswordModalOpen(false)
            setPasswordModalData(null)
          }}
          mindMapId={passwordModalData.id}
          mindMapTitle={passwordModalData.title}
          mode={passwordModalData.mode}
          onSuccess={handlePasswordSuccess}
        />
      )}
      {markdown && (
  <Dialog open onOpenChange={()=>setMarkdown(null)}>
    <DialogContent className="max-w-3xl">
      <DialogHeader>
        <DialogTitle>Texto Gerado (Markdown)</DialogTitle>
      </DialogHeader>

      <Textarea
        className="h-96 font-mono whitespace-pre"
        value={markdown}
        onChange={e=>setMarkdown(e.target.value)}
      />

      <div className="flex justify-end gap-2 pt-4">
        <Button
          variant="outline"
          onClick={()=>{
            const blob = new Blob([markdown],{type:"text/markdown"})
            const url = URL.createObjectURL(blob)
            const a = document.createElement("a")
            a.href=url; a.download="mindmap.md"; a.click()
            URL.revokeObjectURL(url)
          }}
        >
          Baixar .md
        </Button>

        <Button
          onClick={async()=>{
            // @ts-ignore
            const { default: html2pdf } = await import(
              /* webpackIgnore: true */ "html2pdf.js/dist/html2pdf.es.min.js"
            )
            // @ts-ignore
            const { marked } = await import(
              /* webpackIgnore: true */ "https://cdn.jsdelivr.net/npm/marked/marked.esm.js"
            )
            const html = marked.parse(markdown)
            const container = document.createElement("div")
            container.innerHTML = html
            html2pdf().from(container).save("mindmap.pdf")
          }}
        >
          Baixar PDF
        </Button>
      </div>
    </DialogContent>
  </Dialog>
)}

      {/* Modal de Convite para Cadastro */}
      <Dialog open={isSignupInviteOpen} onOpenChange={setIsSignupInviteOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-blue-600" />
              Gostou do seu mapa mental?
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="text-center">
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 mb-2">
                  🎉 Parabéns! Você criou seu primeiro mapa mental
                </h3>
                <p className="text-sm text-gray-600">
                  Este é apenas o começo! Com uma conta, você pode:
                </p>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <Save className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="font-medium text-sm">Salvar seus mapas</p>
                  <p className="text-xs text-gray-500">Acesse seus mapas a qualquer momento</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <Brain className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-sm">Mapas ilimitados</p>
                  <p className="text-xs text-gray-500">Crie quantos mapas quiser, sem limitações</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <Users className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <p className="font-medium text-sm">Compartilhar mapas</p>
                  <p className="text-xs text-gray-500">Colabore com outros usuários</p>
                </div>
              </div>
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                variant="outline"
                onClick={() => setIsSignupInviteOpen(false)}
                className="flex-1"
              >
                Continuar Explorando
              </Button>
              <Button
                onClick={() => {
                  setIsSignupInviteOpen(false)
                  setIsAuthModalOpen(true)
                }}
                className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                Criar Conta Grátis
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
