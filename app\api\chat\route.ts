import { type NextRequest, NextResponse } from "next/server"
import { getConfig } from "@/lib/database"

// Helper function to create a structured mind map from AI response
function createMindMapFromResponse(topic: string, aiResponse: string) {
  let jsonString = aiResponse
  const match = aiResponse.match(/```json\n([\s\S]*?)\n```/)
  if (match) {
    jsonString = match[1]
  }

  try {
    const parsed = JSON.parse(jsonString)
    if (parsed.nodes && parsed.connections) {
      return {
        name: topic,
        description: `Mapa mental gerado por IA para: ${topic}`,
        nodes: parsed.nodes,
        connections: parsed.connections || [],
      }
    }
  } catch (e) {
    console.error("Failed to parse JSON, falling back to text.", e)
  }

  // Create a basic mind map structure from the AI response
  const lines = aiResponse.split("\n").filter((line) => line.trim())
  const nodes = []
  const connections = []

  // Central node
  nodes.push({
    id: "central",
    content: topic,
    x: 400,
    y: 300,
    type: "central",
    color: "#3b82f6",
  })

  // Extract main topics and subtopics
  let nodeId = 1
  let angle = 0
  const angleStep = lines.length > 0 ? (2 * Math.PI) / lines.length : 0

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    if (line && line.length > 3) {
      const cleanText = line
        .replace(/^[-*•]\s*/, "")
        .replace(/^\d+\.\s*/, "")
        .trim()
      if (cleanText) {
        const radius = 200
        const x = 400 + Math.cos(angle) * radius
        const y = 300 + Math.sin(angle) * radius

        const currentNodeId = `node-${nodeId}`
        nodes.push({
          id: currentNodeId,
          content: cleanText.substring(0, 50),
          x: Math.round(x),
          y: Math.round(y),
          type: "branch",
          color: "#10b981",
        })

        connections.push({
          from: "central",
          to: currentNodeId,
        })

        nodeId++
        angle += angleStep
      }
    }
  }

  return {
    name: topic,
    description: `Mapa mental gerado por IA para: ${topic}`,
    nodes,
    connections,
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    const {
      messages,
      model,
      selectedNodeId,
      nodes: existingNodes,
      message: directMessage,
    } = body

    const config = await getConfig()

    const message = directMessage || (messages && messages.length > 0 ? messages[messages.length - 1].content : "")

    if (!message) {
      return NextResponse.json(
        { success: false, error: "Mensagem é obrigatória" },
        { status: 400 },
      )
    }

    let selectedModel = model || config.default_model

    // Alias de modelos
    if (selectedModel) {
      const alias = selectedModel.toLowerCase()
      if (alias.includes("mini")) {
        selectedModel = "openai/gpt-4o-mini"
      } else if (alias.includes("auto")) {
        selectedModel = "tngtech/deepseek-r1t-chimera:free"
      } else if (alias.includes("dev")) {
        selectedModel = "moonshotai/kimi-k2"
      }
    }

    if (!selectedModel) {
      return NextResponse.json(
        { success: false, error: "Nenhum modelo selecionado" },
        { status: 400 },
      )
    }

    let aiResponse = ""
    const isExpansion = selectedNodeId && existingNodes && existingNodes.length > 0

    // Build prompts
    let systemPrompt = ""
    let userPrompt = ""

    if (isExpansion) {
      const targetNode = existingNodes.find((n: any) => n.id === selectedNodeId)
      systemPrompt = `Você é um assistente de mapa mental. Primeiro responda, em até 3 parágrafos, a pergunta ou solicitação do usuário sobre o nó alvo do mapa.\nEm seguida, devolva um objeto JSON COMPACTO com as chaves \"answer\" (texto da resposta resumida em uma frase) e \"newNodes\" (array de objetos {\"content\": string, \"icon\": string?} contendo até 5 ideias de nós filhos, onde \"icon\" é um emoji representativo opcional). Nada além desse objeto JSON deve ser retornado.`
      userPrompt = `Mapa mental existente (simplificado): ${JSON.stringify(
        existingNodes.map((n: any) => n.content),
      )}\nNó alvo: "${
        targetNode?.content || "Nó não encontrado"
      }"\n\nPergunta do usuário: ${message}`
    } else {
      systemPrompt = `Você é um assistente especializado em criar mapas mentais. Sua tarefa é gerar uma estrutura JSON válida para um mapa mental, contendo "nodes" e "connections". Responda SOMENTE com o JSON, sem nenhum texto adicional ou markdown. Exemplo: {"nodes": [{"id": "1", "content": "Nó 1", "x": 100, "y": 100, "level": 0}], "connections": []}`
      userPrompt = `Crie um mapa mental detalhado sobre: ${message}.`
    }

    // Call LLM Provider
    if (config.llm_provider === "openrouter") {
      if (!config.openrouter_api_key) {
        return NextResponse.json(
          {
            success: false,
            error: "Chave da API OpenRouter não configurada",
          },
          { status: 400 },
        )
      }

      // Se for uma chave de teste, criar um mapa mental de demonstração
      if (config.openrouter_api_key === "test-key") {
        const demoMindMap = createMindMapFromResponse(message, `
          Principais conceitos sobre ${message}:
          - Conceito fundamental 1
          - Aspecto importante 2
          - Elemento chave 3
          - Característica relevante 4
          - Aplicação prática 5
        `)
        return NextResponse.json({
          success: true,
          mindMap: demoMindMap,
          rawResponse: "Mapa mental de demonstração gerado"
        })
      }
      const response = await fetch(
        "https://openrouter.ai/api/v1/chat/completions",
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${config.openrouter_api_key}`,
            "Content-Type": "application/json",
            "HTTP-Referer":
              process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000",
            "X-Title": "Mind Map AI",
          },
          body: JSON.stringify({
            model: selectedModel,
            messages: [
              { role: "system", content: systemPrompt },
              { role: "user", content: userPrompt },
            ],
            max_tokens: isExpansion ? 500 : 2000,
            temperature: 0.7,
            response_format: { type: "json_object" },
          }),
        },
      )
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(
          `Erro da API OpenRouter: ${response.status} - ${
            errorData.error?.message || "Erro desconhecido"
          }`,
        )
      }
      const data = await response.json()
      aiResponse = data.choices?.[0]?.message?.content
    } else if (config.llm_provider === "ollama") {
      const ollamaUrl = config.ollama_base_url || "http://localhost:11434"
      const response = await fetch(`${ollamaUrl}/api/generate`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          model: selectedModel,
          prompt: `${systemPrompt}\n${userPrompt}`,
          stream: false,
          format: isExpansion ? "json" : undefined,
        }),
      })
      if (!response.ok) {
        throw new Error(`Erro da API Ollama: ${response.status}`)
      }
      const data = await response.json()
      aiResponse = data.response
    } else {
      return NextResponse.json(
        { success: false, error: "Provedor de IA inválido" },
        { status: 400 },
      )
    }

    if (!aiResponse) {
      throw new Error("Nenhuma resposta recebida do provedor de IA")
    }

    // Handle response based on request type
    if (isExpansion) {
      try {
        // Remove blocos ```json ``` se existirem
        let cleaned = aiResponse.trim()
        const fenceMatch = cleaned.match(/```json[\s\S]*?```/i)
        if (fenceMatch) {
          cleaned = fenceMatch[0].replace(/```json|```/gi, "").trim()
        }

        // Tenta parsear JSON completo (pode ser array ou objeto)
        let parsed: any
        try {
          parsed = JSON.parse(cleaned)
        } catch (_err) {
          // Tentativa extra: extrair da primeira chave/colchete
          const firstBracket = cleaned.search(/[\[{]/)
          const lastBracket = Math.max(cleaned.lastIndexOf("}"), cleaned.lastIndexOf("]"))
          if (firstBracket !== -1 && lastBracket !== -1) {
            parsed = JSON.parse(cleaned.substring(firstBracket, lastBracket + 1))
          } else {
            throw _err
          }
        }

        const answer: string = parsed.answer || ""
        let newNodes: any[] = Array.isArray(parsed)
          ? parsed
          : parsed.newNodes || parsed.nodes || parsed.children || []

        // Se ainda não houver novos nós, tente extrair de texto livre dentro das propriedades
        if (newNodes.length === 0) {
          const textBlob = typeof parsed === "string" ? parsed : JSON.stringify(parsed)
          const lines = textBlob
            .split("\n")
            .map((l) => l.trim().replace(/^[-*•]\s*/, "").replace(/^\d+\.\s*/, ""))
            .filter((l) => !!l && l.length > 1)
          newNodes = lines.map((content: string) => ({ content }))
        }

        return NextResponse.json({ success: true, answer, newNodes })
      } catch (e) {
        console.error("Erro ao analisar a resposta JSON de expansão:", e)
        // Fallback: se o JSON falhar, tente extrair de texto
        const lines = aiResponse
          .split("\n")
          .map((l) => l.trim().replace(/^[-*•]\s*/, "").replace(/^\d+\.\s*/, ""))
          .filter(Boolean)
        const newNodes = lines.map((content: string) => ({ content }))
        return NextResponse.json({ success: true, newNodes })
      }
    } else {
      const mindMap = createMindMapFromResponse(message, aiResponse)
      return NextResponse.json({
        success: true,
        mindMap,
        rawResponse: aiResponse,
      })
    }
  } catch (error) {
    console.error("Erro da API de chat:", error)
    return NextResponse.json(
      {
        success: false,
        error: `Falha no chat: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
      },
      { status: 500 },
    )
  }
}
