{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start"}, "dependencies": {"@aws-sdk/client-rds-data": "latest", "@cloudflare/workers-types": "latest", "@electric-sql/pglite": "latest", "@hookform/resolvers": "^3.9.1", "@libsql/client": "latest", "@libsql/client-wasm": "latest", "@neondatabase/serverless": "latest", "@op-engineering/op-sqlite": "latest", "@opentelemetry/api": "latest", "@planetscale/database": "latest", "@prisma/client": "latest", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-aspect-ratio": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "latest", "@radix-ui/react-navigation-menu": "latest", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "latest", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "latest", "@tidbcloud/serverless": "latest", "@types/better-sqlite3": "latest", "@types/pg": "latest", "@types/sql.js": "latest", "@upstash/redis": "latest", "@vercel/postgres": "latest", "@xata.io/client": "latest", "ai": "latest", "autoprefixer": "^10.4.20", "bcryptjs": "latest", "better-sqlite3": "^8.7.0", "bun-types": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "crypto": "latest", "date-fns": "4.1.0", "drizzle-kit": "latest", "drizzle-orm": "latest", "embla-carousel-react": "latest", "expo-sqlite": "latest", "geist": "^1.3.1", "gel": "latest", "input-otp": "latest", "knex": "latest", "kysely": "latest", "lucide-react": "^0.454.0", "marked": "^16.1.1", "mysql2": "latest", "nanoid": "^5.1.5", "next": "14.2.16", "next-themes": "latest", "path": "latest", "pg": "latest", "postgres": "latest", "react": "^18", "react-day-picker": "latest", "react-dom": "^18", "react-hook-form": "latest", "react-markdown": "^10.1.0", "react-resizable-panels": "latest", "react-scroll": "^1.9.3", "recharts": "latest", "remark-gfm": "^4.0.1", "remark-slug": "^8.0.0", "sonner": "latest", "sql.js": "latest", "sqlite3": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "latest", "zod": "^3.24.1", "zustand": "^5.0.6"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-scroll": "^1.8.10", "html2pdf.js": "^0.10.3", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}